# الخريطة التفاعلية لكلية الحاسبات والمعلومات

## نظرة عامة

صفحة الخرائط التفاعلية لكلية الحاسبات والمعلومات - جامعة الزقازيق هي واجهة تفاعلية متطورة تجمع بين الخرائط الحقيقية وخرائط Google لعرض جميع أقسام ومرافق الكلية بطريقة بصرية جذابة وسهلة الاستخدام.

## المميزات الرئيسية

### 1. الخريطة التفاعلية

- **نقاط تفاعلية**: كل قسم من أقسام الكلية معروض كنقطة ملونة قابلة للنقر على خريطة تفاعلية
- **مواقع حقيقية**: كل نقطة لها إحداثيات نسبية تمثل الموقع الفعلي داخل الكلية
- **تأثيرات بصرية**: تأثيرات نبضية وحركية عند التفاعل مع النقاط
- **خلفية واقعية**: تصميم يحاكي مخطط الكلية الحقيقي مع الطرق والمباني

### 2. خريطة Google المدمجة

- **موقع حقيقي**: iframe مدمج لخرائط Google يعرض الموقع الفعلي للكلية
- **اتجاهات**: إمكانية الحصول على اتجاهات للوصول للكلية
- **تبديل سهل**: إمكانية التبديل بين الخريطة التفاعلية وخريطة Google

### 3. التصميم التفاعلي

- **تأثيرات الحركة**: استخدام Framer Motion لإضافة تأثيرات حركية سلسة
- **استجابة للأجهزة**: التصميم متجاوب مع جميع أحجام الشاشات
- **نوافذ تفاصيل**: نوافذ منبثقة تعرض تفاصيل شاملة عن كل قسم

### 2. الأقسام المتاحة

#### الأقسام الأساسية:

1. **مدخل الكلية** - البوابة الرئيسية
2. **مبنى السكاشن** - الأنشطة الطلابية
3. **مبنى المحاضرات** - قاعات المحاضرات الرئيسية
4. **مكتبة الكلية** - مصادر التعلم والكتب
5. **مدرجات الكلية** - المحاضرات العامة
6. **سكاشن الكلية** - التطبيقات العملية
7. **معامل الكلية** - المختبرات التقنية
8. **مكتب العميد** - الإدارة العليا
9. **الكافيتيريا** - منطقة الاستراحة

#### مكاتب الدكاترة:

- 10 مكاتب للدكاترة (دكتور 1 إلى دكتور 10)

### 4. التفاعل والاستخدام

- **النقر على النقاط**: اضغط على أي نقطة في الخريطة التفاعلية لعرض تفاصيل مفصلة عن القسم
- **التمرير**: مرر الماوس فوق النقاط لرؤية اسم القسم
- **التبديل**: استخدم الأزرار في الأعلى للتبديل بين الخريطة التفاعلية وخريطة Google
- **الإغلاق**: اضغط على زر X لإغلاق نافذة التفاصيل
- **التأثيرات البصرية**: النقاط المحددة تظهر بتأثير نبضي مميز

### 5. الميزات التقنية الجديدة

- **إحداثيات نسبية**: كل قسم له موقع محدد بالنسبة المئوية (x, y) على الخريطة
- **تأثيرات CSS متقدمة**: استخدام backdrop-filter وتأثيرات الشفافية
- **أنماط تفاعلية**: تأثيرات hover وclick مخصصة لكل عنصر
- **خلفية ديناميكية**: خلفية متدرجة تحاكي البيئة الجامعية
- **دليل مرئي**: دليل يوضح أنواع الأقسام المختلفة بالألوان

## التقنيات المستخدمة

### Frontend Framework

- **React 19.1.1**: إطار العمل الأساسي
- **Framer Motion**: للتأثيرات الحركية
- **Tailwind CSS**: للتصميم والأنماط

### الأيقونات

- **React Icons**: مكتبة الأيقونات
  - Font Awesome Icons (fa)
  - Heroicons (hi)
  - Feather Icons (fi)

### الأنماط المخصصة

```css
/* الأنماط الأساسية */
.student-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(31, 38, 135, 0.1);
}

/* أنماط الخريطة التفاعلية */
.interactive-map-point {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
}

.interactive-map-point:hover {
  transform: scale(1.2);
  z-index: 10;
}

.interactive-map-point.selected {
  animation: pulse-ring 2s infinite;
}

@keyframes pulse-ring {
  0% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(59, 130, 246, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
  }
}

/* خلفية الحرم الجامعي */
.campus-background {
  background-image: radial-gradient(
      circle at 25% 25%,
      rgba(59, 130, 246, 0.1) 0%,
      transparent 50%
    ), radial-gradient(
      circle at 75% 75%,
      rgba(16, 185, 129, 0.1) 0%,
      transparent 50%
    ), radial-gradient(circle at 50% 50%, rgba(139, 92, 246, 0.05) 0%, transparent
        50%);
}

/* تحسينات التلميحات */
.map-tooltip {
  backdrop-filter: blur(10px);
  background: rgba(0, 0, 0, 0.85);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* تحسينات النوافذ المنبثقة */
.area-details-modal {
  backdrop-filter: blur(20px);
  background: rgba(255, 255, 255, 0.98);
  border: 2px solid rgba(59, 130, 246, 0.2);
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}
```

## بنية الملفات

```
src/Pages/StudentDashboard/Maps.jsx
├── Import statements
├── College areas data
├── Doctor offices data
├── Component state management
├── Event handlers
├── JSX structure
│   ├── Header section
│   ├── Central college icon
│   ├── Interactive grid
│   ├── Area details panel
│   └── Instructions section
```

## كيفية التخصيص

### إضافة قسم جديد

```javascript
const newArea = {
  id: uniqueId,
  name: "اسم القسم",
  icon: IconComponent,
  color: "from-color-500 to-color-600",
  description: "وصف القسم",
  details: ["تفصيل 1", "تفصيل 2", "تفصيل 3"],
};
```

### تغيير الألوان

يمكن تخصيص ألوان البطاقات من خلال تعديل خاصية `color` في بيانات الأقسام.

### إضافة تأثيرات جديدة

استخدم Framer Motion لإضافة تأثيرات حركية جديدة:

```javascript
<motion.div
  initial={{ opacity: 0 }}
  animate={{ opacity: 1 }}
  transition={{ duration: 0.5 }}
>
  // المحتوى
</motion.div>
```

## الميزات المحققة

- ✅ خريطة تفاعلية مع نقاط قابلة للنقر
- ✅ تكامل مع خرائط Google الحقيقية
- ✅ إحداثيات نسبية دقيقة لكل قسم
- ✅ نوافذ تفاصيل تفاعلية ومتطورة
- ✅ تأثيرات بصرية وحركية متقدمة
- ✅ تصميم متجاوب مع جميع الأجهزة
- ✅ دليل مرئي للأقسام المختلفة

## الاستخدام المستقبلي

- إضافة المزيد من التفاصيل لكل قسم (صور، مواعيد، جهات اتصال)
- ربط الأقسام بقاعدة بيانات ديناميكية
- إضافة نظام بحث وفلترة للأقسام
- تكامل مع نظام الملاحة الداخلية
- إضافة طبقات إضافية للخريطة (مواقف السيارات، المرافق الخدمية)
- دعم اللغات المتعددة

## المتطلبات

- Node.js 16+
- React 19+
- Tailwind CSS 3+
- Framer Motion 12+
- React Icons 5+

## التشغيل

```bash
npm install
npm run dev
```

## الدعم والصيانة

هذا المكون مصمم ليكون قابلاً للصيانة والتوسع بسهولة. يمكن إضافة أقسام جديدة أو تعديل الموجود منها دون الحاجة لتغييرات كبيرة في الكود.
