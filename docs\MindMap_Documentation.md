# الخريطة الذهنية لكلية الحاسبات والمعلومات

## نظرة عامة
صفحة الخرائط الذهنية التفاعلية لكلية الحاسبات والمعلومات - جامعة الزقازيق هي واجهة تفاعلية تعرض جميع أقسام ومرافق الكلية بطريقة بصرية جذابة وسهلة الاستخدام.

## المميزات الرئيسية

### 1. التصميم التفاعلي
- **بطاقات تفاعلية**: كل قسم من أقسام الكلية معروض في بطاقة ملونة مع أيقونة مميزة
- **تأثيرات الحركة**: استخدام Framer Motion لإضافة تأثيرات حركية سلسة
- **استجابة للأجهزة**: التصميم متجاوب مع جميع أحجام الشاشات

### 2. الأقسام المتاحة
#### الأقسام الأساسية:
1. **مدخل الكلية** - البوابة الرئيسية
2. **مبنى السكاشن** - الأنشطة الطلابية
3. **مبنى المحاضرات** - قاعات المحاضرات الرئيسية
4. **مكتبة الكلية** - مصادر التعلم والكتب
5. **مدرجات الكلية** - المحاضرات العامة
6. **سكاشن الكلية** - التطبيقات العملية
7. **معامل الكلية** - المختبرات التقنية
8. **مكتب العميد** - الإدارة العليا
9. **الكافيتيريا** - منطقة الاستراحة

#### مكاتب الدكاترة:
- 10 مكاتب للدكاترة (دكتور 1 إلى دكتور 10)

### 3. التفاعل والاستخدام
- **النقر**: اضغط على أي بطاقة لعرض تفاصيل مفصلة عن القسم
- **التمرير**: مرر الماوس فوق البطاقة لرؤية وصف سريع
- **الإغلاق**: اضغط على زر X لإغلاق التفاصيل

## التقنيات المستخدمة

### Frontend Framework
- **React 19.1.1**: إطار العمل الأساسي
- **Framer Motion**: للتأثيرات الحركية
- **Tailwind CSS**: للتصميم والأنماط

### الأيقونات
- **React Icons**: مكتبة الأيقونات
  - Font Awesome Icons (fa)
  - Heroicons (hi)
  - Feather Icons (fi)

### الأنماط المخصصة
```css
.student-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(31, 38, 135, 0.1);
}

.student-icon-wrapper {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  padding: 0.75rem;
  border-radius: 50%;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.student-gradient-text {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}
```

## بنية الملفات

```
src/Pages/StudentDashboard/Maps.jsx
├── Import statements
├── College areas data
├── Doctor offices data
├── Component state management
├── Event handlers
├── JSX structure
│   ├── Header section
│   ├── Central college icon
│   ├── Interactive grid
│   ├── Area details panel
│   └── Instructions section
```

## كيفية التخصيص

### إضافة قسم جديد
```javascript
const newArea = {
  id: uniqueId,
  name: "اسم القسم",
  icon: IconComponent,
  color: "from-color-500 to-color-600",
  description: "وصف القسم",
  details: ["تفصيل 1", "تفصيل 2", "تفصيل 3"]
};
```

### تغيير الألوان
يمكن تخصيص ألوان البطاقات من خلال تعديل خاصية `color` في بيانات الأقسام.

### إضافة تأثيرات جديدة
استخدم Framer Motion لإضافة تأثيرات حركية جديدة:
```javascript
<motion.div
  initial={{ opacity: 0 }}
  animate={{ opacity: 1 }}
  transition={{ duration: 0.5 }}
>
  // المحتوى
</motion.div>
```

## الاستخدام المستقبلي
- إمكانية إضافة خرائط حقيقية للكلية
- ربط الأقسام بمعلومات أكثر تفصيلاً
- إضافة نظام بحث للأقسام
- تكامل مع نظام الملاحة GPS

## المتطلبات
- Node.js 16+
- React 19+
- Tailwind CSS 3+
- Framer Motion 12+
- React Icons 5+

## التشغيل
```bash
npm install
npm run dev
```

## الدعم والصيانة
هذا المكون مصمم ليكون قابلاً للصيانة والتوسع بسهولة. يمكن إضافة أقسام جديدة أو تعديل الموجود منها دون الحاجة لتغييرات كبيرة في الكود.
