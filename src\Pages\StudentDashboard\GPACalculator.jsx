import React, { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  FiPlus,
  FiTrash2,
  
  <PERSON>A<PERSON>,
  <PERSON>T<PERSON>dingUp,
  <PERSON>BookO<PERSON>,
  FiTarget,
} from "react-icons/fi";
import { MdCalculate as FiCalculator } from 'react-icons/md';

const GPACalculator = () => {
  const [numSubjects, setNumSubjects] = useState("");
  const [subjects, setSubjects] = useState([]);
  const [currentStep, setCurrentStep] = useState(1);
  const [gpa, setGpa] = useState(null);
  const [errors, setErrors] = useState({});

  // Grade point mapping
  const gradePoints = {
    A: 4.0,
    "B+": 3.5,
    B: 3.0,
    "C+": 2.5,
    C: 2.0,
    D: 1.0,
    F: 0.0,
  };

  // Initialize subjects when number is set
  useEffect(() => {
    if (numSubjects && parseInt(numSubjects) > 0) {
      const newSubjects = Array.from(
        { length: parseInt(numSubjects) },
        (_, index) => ({
          id: index + 1,
          name: "",
          creditHours: "",
          grade: "",
        })
      );
      setSubjects(newSubjects);
      setCurrentStep(2);
    }
  }, [numSubjects]);

  // Add a new subject
  const addSubject = () => {
    const newSubject = {
      id: subjects.length + 1,
      name: "",
      creditHours: "",
      grade: "",
    };
    setSubjects([...subjects, newSubject]);
  };

  // Remove a subject
  const removeSubject = (id) => {
    setSubjects(subjects.filter((subject) => subject.id !== id));
  };

  // Update subject data
  const updateSubject = (id, field, value) => {
    setSubjects(
      subjects.map((subject) =>
        subject.id === id ? { ...subject, [field]: value } : subject
      )
    );
    // Clear errors for this field
    if (errors[`${id}-${field}`]) {
      setErrors((prev) => {
        const newErrors = { ...prev };
        delete newErrors[`${id}-${field}`];
        return newErrors;
      });
    }
  };

  // Validate inputs
  const validateInputs = () => {
    const newErrors = {};

    subjects.forEach((subject) => {
      if (!subject.name.trim()) {
        newErrors[`${subject.id}-name`] = "Subject name is required";
      }
      if (!subject.creditHours || parseInt(subject.creditHours) <= 0) {
        newErrors[`${subject.id}-creditHours`] = "Valid credit hours required";
      }
      if (!subject.grade) {
        newErrors[`${subject.id}-grade`] = "Grade is required";
      }
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Calculate GPA
  const calculateGPA = () => {
    if (!validateInputs()) return;

    let totalPoints = 0;
    let totalCredits = 0;

    subjects.forEach((subject) => {
      const credits = parseInt(subject.creditHours);
      const points = gradePoints[subject.grade];
      totalPoints += credits * points;
      totalCredits += credits;
    });

    const calculatedGPA =
      totalCredits > 0 ? (totalPoints / totalCredits).toFixed(2) : 0;
    setGpa(parseFloat(calculatedGPA));
    setCurrentStep(3);
  };

  // Get motivational message based on GPA
  const getMotivationalMessage = (gpa) => {
    if (gpa >= 3.7)
      return {
        text: "Excellent work! Outstanding performance! 🌟",
        color: "text-green-600",
        bg: "bg-green-50",
      };
    if (gpa >= 3.3)
      return {
        text: "Great job! Very good performance! 👏",
        color: "text-blue-600",
        bg: "bg-blue-50",
      };
    if (gpa >= 3.0)
      return {
        text: "Good work! Keep it up! 💪",
        color: "text-indigo-600",
        bg: "bg-indigo-50",
      };
    if (gpa >= 2.5)
      return {
        text: "You're doing well! Room for improvement! 📈",
        color: "text-yellow-600",
        bg: "bg-yellow-50",
      };
    if (gpa >= 2.0)
      return {
        text: "You can improve! Keep working hard! 🎯",
        color: "text-orange-600",
        bg: "bg-orange-50",
      };
    return {
      text: "Don't give up! Every step counts! 💫",
      color: "text-red-600",
      bg: "bg-red-50",
    };
  };

  // Reset calculator
  const resetCalculator = () => {
    setNumSubjects("");
    setSubjects([]);
    setCurrentStep(1);
    setGpa(null);
    setErrors({});
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 p-6">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-8"
        >
          <div className="flex items-center justify-center mb-4">
            <div className="bg-gradient-to-r from-blue-500 to-indigo-600 p-4 rounded-full shadow-lg">
              <FiCalculator className="w-8 h-8 text-white" />
            </div>
          </div>
          <h1 className="text-4xl font-bold text-gray-800 mb-2">
            GPA Calculator
          </h1>
          <p className="text-lg text-gray-600">
            Calculate your semester GPA with ease
          </p>
        </motion.div>

        {/* Progress Steps */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.3, duration: 0.5 }}
          className="flex justify-center mb-8"
        >
          <div className="flex items-center space-x-4">
            {[1, 2, 3].map((step) => (
              <div key={step} className="flex items-center">
                <div
                  className={`w-10 h-10 rounded-full flex items-center justify-center font-bold text-sm transition-all duration-300 ${
                    currentStep >= step
                      ? "bg-gradient-to-r from-blue-500 to-indigo-600 text-white shadow-lg"
                      : "bg-gray-200 text-gray-500"
                  }`}
                >
                  {step}
                </div>
                {step < 3 && (
                  <div
                    className={`w-12 h-1 mx-2 transition-all duration-300 ${
                      currentStep > step
                        ? "bg-gradient-to-r from-blue-500 to-indigo-600"
                        : "bg-gray-200"
                    }`}
                  />
                )}
              </div>
            ))}
          </div>
        </motion.div>

        {/* Step 1: Number of Subjects */}
        <AnimatePresence mode="wait">
          {currentStep === 1 && (
            <motion.div
              key="step1"
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -50 }}
              transition={{ duration: 0.5 }}
              className="bg-white rounded-2xl shadow-xl p-8 border border-gray-100"
            >
              <div className="text-center">
                <div className="bg-gradient-to-r from-blue-100 to-indigo-100 p-6 rounded-full w-20 h-20 mx-auto mb-6 flex items-center justify-center">
                  <FiBookOpen className="w-10 h-10 text-blue-600" />
                </div>
                <h2 className="text-2xl font-bold text-gray-800 mb-4">
                  How many subjects did you study this semester?
                </h2>
                <p className="text-gray-600 mb-8">
                  Enter the total number of subjects to get started
                </p>
                <div className="max-w-xs mx-auto">
                  <input
                    type="number"
                    min="1"
                    max="20"
                    value={numSubjects}
                    onChange={(e) => setNumSubjects(e.target.value)}
                    placeholder="Enter number of subjects"
                    className="w-full px-6 py-4 text-center text-xl font-semibold border-2 border-gray-200 rounded-xl focus:border-blue-500 focus:ring-4 focus:ring-blue-100 transition-all duration-300 outline-none"
                  />
                </div>
              </div>
            </motion.div>
          )}

          {/* Step 2: Subject Details */}
          {currentStep === 2 && (
            <motion.div
              key="step2"
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -50 }}
              transition={{ duration: 0.5 }}
              className="space-y-6"
            >
              <div className="bg-white rounded-2xl shadow-xl p-8 border border-gray-100">
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center space-x-4">
                    <div className="bg-gradient-to-r from-indigo-100 to-purple-100 p-3 rounded-full">
                      <FiTarget className="w-6 h-6 text-indigo-600" />
                    </div>
                    <div>
                      <h2 className="text-2xl font-bold text-gray-800">
                        Subject Details
                      </h2>
                      <p className="text-gray-600">
                        Fill in the details for each subject
                      </p>
                    </div>
                  </div>
                  <button
                    onClick={addSubject}
                    className="flex items-center space-x-2 bg-gradient-to-r from-green-500 to-emerald-600 text-white px-4 py-2 rounded-lg hover:shadow-lg transition-all duration-300 hover:scale-105"
                  >
                    <FiPlus className="w-4 h-4" />
                    <span>Add Subject</span>
                  </button>
                </div>

                <div className="space-y-4 max-h-96 overflow-y-auto">
                  {subjects.map((subject, index) => (
                    <motion.div
                      key={subject.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.1, duration: 0.3 }}
                      className="bg-gradient-to-r from-gray-50 to-blue-50 rounded-xl p-6 border border-gray-200 hover:shadow-md transition-all duration-300"
                    >
                      <div className="flex items-center justify-between mb-4">
                        <h3 className="text-lg font-semibold text-gray-700">
                          Subject {subject.id}
                        </h3>
                        {subjects.length > 1 && (
                          <button
                            onClick={() => removeSubject(subject.id)}
                            className="text-red-500 hover:text-red-700 hover:bg-red-50 p-2 rounded-lg transition-all duration-300"
                          >
                            <FiTrash2 className="w-4 h-4" />
                          </button>
                        )}
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        {/* Subject Name */}
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Subject Name
                          </label>
                          <input
                            type="text"
                            value={subject.name}
                            onChange={(e) =>
                              updateSubject(subject.id, "name", e.target.value)
                            }
                            placeholder="e.g., Mathematics"
                            className={`w-full px-4 py-3 border-2 rounded-lg focus:ring-4 focus:ring-blue-100 transition-all duration-300 outline-none ${
                              errors[`${subject.id}-name`]
                                ? "border-red-300 focus:border-red-500"
                                : "border-gray-200 focus:border-blue-500"
                            }`}
                          />
                          {errors[`${subject.id}-name`] && (
                            <p className="text-red-500 text-xs mt-1">
                              {errors[`${subject.id}-name`]}
                            </p>
                          )}
                        </div>

                        {/* Credit Hours */}
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Credit Hours
                          </label>
                          <input
                            type="number"
                            min="1"
                            max="6"
                            value={subject.creditHours}
                            onChange={(e) =>
                              updateSubject(
                                subject.id,
                                "creditHours",
                                e.target.value
                              )
                            }
                            placeholder="3"
                            className={`w-full px-4 py-3 border-2 rounded-lg focus:ring-4 focus:ring-blue-100 transition-all duration-300 outline-none ${
                              errors[`${subject.id}-creditHours`]
                                ? "border-red-300 focus:border-red-500"
                                : "border-gray-200 focus:border-blue-500"
                            }`}
                          />
                          {errors[`${subject.id}-creditHours`] && (
                            <p className="text-red-500 text-xs mt-1">
                              {errors[`${subject.id}-creditHours`]}
                            </p>
                          )}
                        </div>

                        {/* Grade */}
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Grade
                          </label>
                          <select
                            value={subject.grade}
                            onChange={(e) =>
                              updateSubject(subject.id, "grade", e.target.value)
                            }
                            className={`w-full px-4 py-3 border-2 rounded-lg focus:ring-4 focus:ring-blue-100 transition-all duration-300 outline-none ${
                              errors[`${subject.id}-grade`]
                                ? "border-red-300 focus:border-red-500"
                                : "border-gray-200 focus:border-blue-500"
                            }`}
                          >
                            <option value="">Select Grade</option>
                            {Object.keys(gradePoints).map((grade) => (
                              <option key={grade} value={grade}>
                                {grade} ({gradePoints[grade].toFixed(1)})
                              </option>
                            ))}
                          </select>
                          {errors[`${subject.id}-grade`] && (
                            <p className="text-red-500 text-xs mt-1">
                              {errors[`${subject.id}-grade`]}
                            </p>
                          )}
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </div>

                <div className="flex justify-between mt-8">
                  <button
                    onClick={resetCalculator}
                    className="px-6 py-3 border-2 border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-all duration-300"
                  >
                    Start Over
                  </button>
                  <button
                    onClick={calculateGPA}
                    className="px-8 py-3 bg-gradient-to-r from-blue-500 to-indigo-600 text-white rounded-lg hover:shadow-lg transition-all duration-300 hover:scale-105 flex items-center space-x-2"
                  >
                    <FiCalculator className="w-4 h-4" />
                    <span>Calculate GPA</span>
                  </button>
                </div>
              </div>
            </motion.div>
          )}

          {/* Step 3: GPA Results */}
          {currentStep === 3 && gpa !== null && (
            <motion.div
              key="step3"
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.8 }}
              transition={{ duration: 0.6, type: "spring", bounce: 0.3 }}
              className="bg-white rounded-2xl shadow-2xl p-8 border border-gray-100"
            >
              <div className="text-center">
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{
                    delay: 0.3,
                    duration: 0.5,
                    type: "spring",
                    bounce: 0.5,
                  }}
                  className="bg-gradient-to-r from-yellow-100 to-orange-100 p-6 rounded-full w-24 h-24 mx-auto mb-6 flex items-center justify-center"
                >
                  <FiAward className="w-12 h-12 text-yellow-600" />
                </motion.div>

                <motion.h2
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.4, duration: 0.5 }}
                  className="text-3xl font-bold text-gray-800 mb-4"
                >
                  Your Semester GPA
                </motion.h2>

                <motion.div
                  initial={{ opacity: 0, scale: 0.5 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{
                    delay: 0.5,
                    duration: 0.6,
                    type: "spring",
                    bounce: 0.4,
                  }}
                  className="bg-gradient-to-r from-blue-500 to-indigo-600 text-white rounded-2xl p-8 mb-6 shadow-lg"
                >
                  <div className="text-6xl font-black mb-2">{gpa}</div>
                  <div className="text-xl font-semibold opacity-90">
                    out of 4.0
                  </div>
                </motion.div>

                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.6, duration: 0.5 }}
                  className={`${getMotivationalMessage(gpa).bg} ${
                    getMotivationalMessage(gpa).color
                  } rounded-xl p-6 mb-8 border-2 border-opacity-20`}
                >
                  <div className="flex items-center justify-center space-x-3">
                    <FiTrendingUp className="w-6 h-6" />
                    <span className="text-lg font-semibold">
                      {getMotivationalMessage(gpa).text}
                    </span>
                  </div>
                </motion.div>

                {/* Subject Summary */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.7, duration: 0.5 }}
                  className="bg-gray-50 rounded-xl p-6 mb-8"
                >
                  <h3 className="text-lg font-semibold text-gray-800 mb-4">
                    Subject Summary
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="bg-white rounded-lg p-4 shadow-sm">
                      <div className="text-2xl font-bold text-blue-600">
                        {subjects.length}
                      </div>
                      <div className="text-sm text-gray-600">
                        Total Subjects
                      </div>
                    </div>
                    <div className="bg-white rounded-lg p-4 shadow-sm">
                      <div className="text-2xl font-bold text-green-600">
                        {subjects.reduce(
                          (total, subject) =>
                            total + parseInt(subject.creditHours || 0),
                          0
                        )}
                      </div>
                      <div className="text-sm text-gray-600">
                        Total Credit Hours
                      </div>
                    </div>
                  </div>
                </motion.div>

                {/* Action Buttons */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.8, duration: 0.5 }}
                  className="flex flex-col sm:flex-row gap-4 justify-center"
                >
                  <button
                    onClick={() => setCurrentStep(2)}
                    className="px-6 py-3 border-2 border-blue-500 text-blue-600 rounded-lg hover:bg-blue-50 transition-all duration-300 flex items-center justify-center space-x-2"
                  >
                    <span>Edit Subjects</span>
                  </button>
                  <button
                    onClick={resetCalculator}
                    className="px-6 py-3 bg-gradient-to-r from-blue-500 to-indigo-600 text-white rounded-lg hover:shadow-lg transition-all duration-300 hover:scale-105 flex items-center justify-center space-x-2"
                  >
                    <FiCalculator className="w-4 h-4" />
                    <span>New Calculation</span>
                  </button>
                </motion.div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Tips Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.9, duration: 0.5 }}
          className="mt-8 bg-white rounded-2xl shadow-xl p-8 border border-gray-100"
        >
          <h3 className="text-xl font-bold text-gray-800 mb-4 flex items-center space-x-3">
            <div className="bg-gradient-to-r from-purple-100 to-pink-100 p-2 rounded-lg">
              <FiTarget className="w-5 h-5 text-purple-600" />
            </div>
            <span>GPA Scale & Tips</span>
          </h3>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-semibold text-gray-700 mb-3">Grade Scale:</h4>
              <div className="space-y-2">
                {Object.entries(gradePoints).map(([grade, points]) => (
                  <div
                    key={grade}
                    className="flex justify-between items-center py-2 px-3 bg-gray-50 rounded-lg"
                  >
                    <span className="font-medium text-gray-700">{grade}</span>
                    <span className="text-blue-600 font-semibold">
                      {points.toFixed(1)}
                    </span>
                  </div>
                ))}
              </div>
            </div>

            <div>
              <h4 className="font-semibold text-gray-700 mb-3">
                Tips for Success:
              </h4>
              <ul className="space-y-2 text-sm text-gray-600">
                <li className="flex items-start space-x-2">
                  <span className="text-blue-500 mt-1">•</span>
                  <span>
                    Focus on high-credit courses for maximum GPA impact
                  </span>
                </li>
                <li className="flex items-start space-x-2">
                  <span className="text-blue-500 mt-1">•</span>
                  <span>
                    Maintain consistent study habits throughout the semester
                  </span>
                </li>
                <li className="flex items-start space-x-2">
                  <span className="text-blue-500 mt-1">•</span>
                  <span>
                    Seek help early if you're struggling with any subject
                  </span>
                </li>
                <li className="flex items-start space-x-2">
                  <span className="text-blue-500 mt-1">•</span>
                  <span>Use office hours and study groups effectively</span>
                </li>
              </ul>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default GPACalculator;
