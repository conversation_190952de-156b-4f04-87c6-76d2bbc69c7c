import React, { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  FaMap as FiMap,
  FaUniversity,
  FaBook,
  FaChalkboardTeacher,
  FaFlask,
  FaUserTie,
  FaUserGraduate,
  FaCoffee,
  FaBuilding,
  FaUsers,
} from "react-icons/fa";
import { HiAcademicCap } from "react-icons/hi";

// بيانات أقسام الكلية
const collegeAreas = [
  {
    id: 1,
    name: "مدخل الكلية",
    icon: FaUniversity,
    color: "from-blue-500 to-blue-600",
    description: "البوابة الرئيسية لكلية الحاسبات والمعلومات",
    details: ["استقبال الطلاب", "مكتب الأمن", "لوحة الإعلانات الرئيسية"],
  },
  {
    id: 2,
    name: "مبنى السكاشن",
    icon: FaBuilding,
    color: "from-green-500 to-green-600",
    description: "مبنى السكاشن والأنشطة الطلابية",
    details: ["قاعات السكاشن", "مكتب شؤون الطلاب", "الأنشطة الطلابية"],
  },
  {
    id: 3,
    name: "مبنى المحاضرات",
    icon: FaChalkboardTeacher,
    color: "from-purple-500 to-purple-600",
    description: "قاعات المحاضرات الرئيسية",
    details: ["قاعة 1", "قاعة 2", "قاعة 3", "قاعة المؤتمرات"],
  },
  {
    id: 4,
    name: "مكتبة الكلية",
    icon: FaBook,
    color: "from-orange-500 to-orange-600",
    description: "مكتبة الكلية ومصادر التعلم",
    details: ["قسم الكتب", "قسم المراجع", "قاعة المطالعة", "الكتب الإلكترونية"],
  },
  {
    id: 5,
    name: "مدرجات الكلية",
    icon: HiAcademicCap,
    color: "from-red-500 to-red-600",
    description: "المدرجات الكبيرة للمحاضرات العامة",
    details: ["المدرج الكبير", "المدرج الصغير", "قاعة الاجتماعات"],
  },
  {
    id: 6,
    name: "سكاشن الكلية",
    icon: FaUsers,
    color: "from-teal-500 to-teal-600",
    description: "قاعات السكاشن والتطبيقات العملية",
    details: ["سكشن 1", "سكشن 2", "سكشن 3", "سكشن 4"],
  },
  {
    id: 7,
    name: "معامل الكلية",
    icon: FaFlask,
    color: "from-indigo-500 to-indigo-600",
    description: "المعامل والمختبرات التقنية",
    details: [
      "معمل البرمجة",
      "معمل الشبكات",
      "معمل الأجهزة",
      "معمل الذكي الاصطناعي",
    ],
  },
  {
    id: 8,
    name: "مكتب العميد",
    icon: FaUserTie,
    color: "from-yellow-500 to-yellow-600",
    description: "مكتب عميد الكلية والإدارة العليا",
    details: ["مكتب العميد", "مكتب وكيل الكلية", "سكرتارية العمادة"],
  },
  {
    id: 9,
    name: "الكافيتيريا",
    icon: FaCoffee,
    color: "from-pink-500 to-pink-600",
    description: "كافيتيريا الكلية ومنطقة الاستراحة",
    details: ["منطقة الطعام", "منطقة الجلوس", "آلات البيع"],
  },
];

// مكاتب الدكاترة
const doctorOffices = Array.from({ length: 10 }, (_, i) => ({
  id: 10 + i,
  name: `مكتب دكتور ${i + 1}`,
  icon: FaUserGraduate,
  color: "from-gray-500 to-gray-600",
  description: `مكتب الدكتور رقم ${i + 1}`,
  details: ["ساعات مكتبية", "استشارات أكاديمية", "مراجعة المشاريع"],
}));

const allAreas = [...collegeAreas, ...doctorOffices];

const Maps = () => {
  const [selectedArea, setSelectedArea] = useState(null);
  const [hoveredArea, setHoveredArea] = useState(null);

  const handleAreaClick = (area) => {
    setSelectedArea(selectedArea?.id === area.id ? null : area);
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="space-y-6"
    >
      {/* Header */}
      <div className="student-card rounded-2xl p-6">
        <div className="flex items-center space-x-4 mb-6">
          <div className="student-icon-wrapper">
            <FiMap className="text-2xl text-blue-600" />
          </div>
          <div>
            <h1 className="text-3xl font-bold student-gradient-text arabic-text">
              الخريطة الذهنية لكلية الحاسبات والمعلومات
            </h1>
            <p className="text-gray-600 arabic-text">
              استكشف أقسام ومرافق كلية الحاسبات والمعلومات - جامعة الزقازيق
            </p>
          </div>
        </div>
      </div>

      {/* Mind Map Grid */}
      <div className="student-card rounded-2xl p-8">
        <div className="text-center mb-8">
          <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full mb-4 shadow-lg">
            <FaUniversity className="text-3xl text-white" />
          </div>
          <h2 className="text-2xl font-bold text-gray-800 mb-2">
            كلية الحاسبات والمعلومات
          </h2>
          <p className="text-gray-600">جامعة الزقازيق</p>
        </div>

        {/* Areas Grid */}
        <div className="mindmap-grid grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-8">
          {allAreas.map((area, index) => {
            const IconComponent = area.icon;
            return (
              <motion.div
                key={area.id}
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                whileHover={{ scale: 1.05, y: -5 }}
                whileTap={{ scale: 0.95 }}
                className={`
                  mindmap-card relative cursor-pointer rounded-xl p-6 text-center
                  bg-gradient-to-br ${area.color} text-white
                  shadow-lg hover:shadow-xl transition-all duration-300
                  ${
                    selectedArea?.id === area.id
                      ? "ring-4 ring-blue-300 ring-opacity-50"
                      : ""
                  }
                `}
                onClick={() => handleAreaClick(area)}
                onMouseEnter={() => setHoveredArea(area)}
                onMouseLeave={() => setHoveredArea(null)}
              >
                <div className="flex flex-col items-center space-y-3">
                  <div className="bg-white bg-opacity-20 p-3 rounded-full">
                    <IconComponent className="text-2xl" />
                  </div>
                  <h3 className="font-bold text-sm">{area.name}</h3>
                </div>

                {/* Hover Tooltip */}
                {hoveredArea?.id === area.id && (
                  <motion.div
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 z-10"
                  >
                    <div className="mindmap-tooltip text-white text-xs rounded-lg px-3 py-2 whitespace-nowrap shadow-lg">
                      {area.description}
                      <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900"></div>
                    </div>
                  </motion.div>
                )}
              </motion.div>
            );
          })}
        </div>

        {/* Selected Area Details */}
        <AnimatePresence>
          {selectedArea && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: "auto" }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.3 }}
              className="mindmap-details bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-6 border border-blue-200"
            >
              <div className="flex items-start space-x-4">
                <div
                  className={`bg-gradient-to-br ${selectedArea.color} p-4 rounded-xl text-white shadow-lg`}
                >
                  <selectedArea.icon className="text-2xl" />
                </div>
                <div className="flex-1">
                  <h3 className="text-xl font-bold text-gray-800 mb-2">
                    {selectedArea.name}
                  </h3>
                  <p className="text-gray-600 mb-4">
                    {selectedArea.description}
                  </p>
                  <div className="space-y-2">
                    <h4 className="font-semibold text-gray-700">التفاصيل:</h4>
                    <ul className="grid grid-cols-1 md:grid-cols-2 gap-2">
                      {selectedArea.details.map((detail, index) => (
                        <li
                          key={index}
                          className="flex items-center space-x-2 text-gray-600"
                        >
                          <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                          <span className="text-sm">{detail}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
                <button
                  onClick={() => setSelectedArea(null)}
                  className="text-gray-400 hover:text-gray-600 transition-colors"
                >
                  <svg
                    className="w-6 h-6"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M6 18L18 6M6 6l12 12"
                    />
                  </svg>
                </button>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Instructions */}
      <div className="student-card rounded-2xl p-6">
        <div className="text-center">
          <h3 className="text-lg font-semibold text-gray-800 mb-2">
            كيفية الاستخدام
          </h3>
          <p className="text-gray-600 text-sm">
            اضغط على أي بطاقة لعرض تفاصيل أكثر عن القسم أو المرفق. مرر الماوس
            فوق البطاقة لرؤية وصف سريع.
          </p>
        </div>
      </div>
    </motion.div>
  );
};

export default Maps;
