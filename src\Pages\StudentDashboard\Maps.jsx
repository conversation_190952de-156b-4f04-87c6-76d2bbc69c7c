import React, { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  FaMap as FiMap,
  FaUniversity,
  FaBook,
  FaChalkboardTeacher,
  FaFlask,
  FaUserTie,
  FaUserGraduate,
  FaCoffee,
  FaBuilding,
  FaUsers,
  FaMapMarkerAlt,
  FaTimes,
  FaInfoCircle,
} from "react-icons/fa";
import { HiAcademicCap } from "react-icons/hi";

// بيانات أقسام الكلية مع الإحداثيات النسبية على الخريطة
const collegeAreas = [
  {
    id: 1,
    name: "مدخل الكلية",
    icon: FaUniversity,
    color: "from-blue-500 to-blue-600",
    description: "البوابة الرئيسية لكلية الحاسبات والمعلومات",
    details: ["استقبال الطلاب", "مكتب الأمن", "لوحة الإعلانات الرئيسية"],
    position: { x: 15, y: 85 }, // النسبة المئوية من اليسار والأعلى
  },
  {
    id: 2,
    name: "مبنى السكاشن",
    icon: FaBuilding,
    color: "from-green-500 to-green-600",
    description: "مبنى السكاشن والأنشطة الطلابية",
    details: ["قاعات السكاشن", "مكتب شؤون الطلاب", "الأنشطة الطلابية"],
    position: { x: 25, y: 60 },
  },
  {
    id: 3,
    name: "مبنى المحاضرات",
    icon: FaChalkboardTeacher,
    color: "from-purple-500 to-purple-600",
    description: "قاعات المحاضرات الرئيسية",
    details: ["قاعة 1", "قاعة 2", "قاعة 3", "قاعة المؤتمرات"],
    position: { x: 45, y: 40 },
  },
  {
    id: 4,
    name: "مكتبة الكلية",
    icon: FaBook,
    color: "from-orange-500 to-orange-600",
    description: "مكتبة الكلية ومصادر التعلم",
    details: ["قسم الكتب", "قسم المراجع", "قاعة المطالعة", "الكتب الإلكترونية"],
    position: { x: 65, y: 30 },
  },
  {
    id: 5,
    name: "مدرجات الكلية",
    icon: HiAcademicCap,
    color: "from-red-500 to-red-600",
    description: "المدرجات الكبيرة للمحاضرات العامة",
    details: ["المدرج الكبير", "المدرج الصغير", "قاعة الاجتماعات"],
    position: { x: 75, y: 50 },
  },
  {
    id: 6,
    name: "سكاشن الكلية",
    icon: FaUsers,
    color: "from-teal-500 to-teal-600",
    description: "قاعات السكاشن والتطبيقات العملية",
    details: ["سكشن 1", "سكشن 2", "سكشن 3", "سكشن 4"],
    position: { x: 55, y: 70 },
  },
  {
    id: 7,
    name: "معامل الكلية",
    icon: FaFlask,
    color: "from-indigo-500 to-indigo-600",
    description: "المعامل والمختبرات التقنية",
    details: [
      "معمل البرمجة",
      "معمل الشبكات",
      "معمل الأجهزة",
      "معمل الذكي الاصطناعي",
    ],
    position: { x: 35, y: 25 },
  },
  {
    id: 8,
    name: "مكتب العميد",
    icon: FaUserTie,
    color: "from-yellow-500 to-yellow-600",
    description: "مكتب عميد الكلية والإدارة العليا",
    details: ["مكتب العميد", "مكتب وكيل الكلية", "سكرتارية العمادة"],
    position: { x: 80, y: 20 },
  },
  {
    id: 9,
    name: "الكافيتيريا",
    icon: FaCoffee,
    color: "from-pink-500 to-pink-600",
    description: "كافيتيريا الكلية ومنطقة الاستراحة",
    details: ["منطقة الطعام", "منطقة الجلوس", "آلات البيع"],
    position: { x: 20, y: 45 },
  },
];

// مكاتب الدكاترة
const doctorOffices = Array.from({ length: 10 }, (_, i) => ({
  id: 10 + i,
  name: `مكتب دكتور ${i + 1}`,
  icon: FaUserGraduate,
  color: "from-gray-500 to-gray-600",
  description: `مكتب الدكتور رقم ${i + 1}`,
  details: ["ساعات مكتبية", "استشارات أكاديمية", "مراجعة المشاريع"],
  position: { x: 85 + (i % 2) * 5, y: 35 + i * 5 },
}));

const allAreas = [...collegeAreas, ...doctorOffices];

const Maps = () => {
  const [selectedArea, setSelectedArea] = useState(null);
  const [showInteractiveMap, setShowInteractiveMap] = useState(true);

  const handleAreaClick = (area) => {
    setSelectedArea(selectedArea?.id === area.id ? null : area);
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="space-y-6"
    >
      {/* Header */}
      <div className="student-card rounded-2xl p-6">
        <div className="flex items-center space-x-4 mb-6">
          <div className="student-icon-wrapper">
            <FiMap className="text-2xl text-blue-600" />
          </div>
          <div>
            <h1 className="text-3xl font-bold student-gradient-text arabic-text">
              الخريطة التفاعلية لكلية الحاسبات والمعلومات
            </h1>
            <p className="text-gray-600 arabic-text">
              استكشف أقسام ومرافق كلية الحاسبات والمعلومات - جامعة الزقازيق
            </p>
          </div>
        </div>

        {/* Toggle Buttons */}
        <div className="flex space-x-4 mb-4">
          <button
            onClick={() => setShowInteractiveMap(true)}
            className={`px-4 py-2 rounded-lg font-medium transition-all ${
              showInteractiveMap
                ? "bg-blue-500 text-white shadow-lg"
                : "bg-gray-200 text-gray-700 hover:bg-gray-300"
            }`}
          >
            الخريطة التفاعلية
          </button>
          <button
            onClick={() => setShowInteractiveMap(false)}
            className={`px-4 py-2 rounded-lg font-medium transition-all ${
              !showInteractiveMap
                ? "bg-blue-500 text-white shadow-lg"
                : "bg-gray-200 text-gray-700 hover:bg-gray-300"
            }`}
          >
            خريطة Google
          </button>
        </div>
      </div>

      {/* Interactive Map or Google Map */}
      <div className="student-card rounded-2xl p-8">
        {showInteractiveMap ? (
          // Interactive Campus Map
          <div>
            <div className="text-center mb-8">
              <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full mb-4 shadow-lg">
                <FaUniversity className="text-3xl text-white" />
              </div>
              <h2 className="text-2xl font-bold text-gray-800 mb-2 arabic-text">
                كلية الحاسبات والمعلومات
              </h2>
              <p className="text-gray-600 arabic-text">
                جامعة الزقازيق - الخريطة التفاعلية
              </p>
            </div>

            {/* Interactive Map Container */}
            <div
              className="relative campus-background rounded-xl overflow-hidden shadow-lg border border-gray-200"
              style={{ height: "500px" }}
            >
              {/* Campus Background */}
              <div className="absolute inset-0 bg-gradient-to-br from-green-100 via-blue-100 to-indigo-100 opacity-70"></div>

              {/* Campus Roads/Paths */}
              <svg
                className="absolute inset-0 w-full h-full"
                viewBox="0 0 100 100"
                preserveAspectRatio="none"
              >
                <path
                  d="M10,80 Q30,70 50,60 T90,40"
                  stroke="#8B5CF6"
                  strokeWidth="0.5"
                  fill="none"
                  opacity="0.6"
                />
                <path
                  d="M20,20 L80,20 L80,80 L20,80 Z"
                  stroke="#6B7280"
                  strokeWidth="0.3"
                  fill="none"
                  opacity="0.4"
                />
                <path
                  d="M10,50 L90,50"
                  stroke="#6B7280"
                  strokeWidth="0.3"
                  fill="none"
                  opacity="0.4"
                />
                <path
                  d="M50,10 L50,90"
                  stroke="#6B7280"
                  strokeWidth="0.3"
                  fill="none"
                  opacity="0.4"
                />
              </svg>

              {/* Interactive Points */}
              {allAreas.map((area) => {
                const IconComponent = area.icon;
                return (
                  <motion.div
                    key={area.id}
                    className="absolute cursor-pointer transform -translate-x-1/2 -translate-y-1/2"
                    style={{
                      left: `${area.position.x}%`,
                      top: `${area.position.y}%`,
                    }}
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    transition={{ duration: 0.5, delay: area.id * 0.1 }}
                    whileHover={{ scale: 1.2, zIndex: 10 }}
                    whileTap={{ scale: 0.9 }}
                    onClick={() => handleAreaClick(area)}
                  >
                    <div
                      className={`
                      interactive-map-point relative w-12 h-12 rounded-full shadow-lg
                      bg-gradient-to-br ${area.color} text-white
                      flex items-center justify-center
                      ${
                        selectedArea?.id === area.id
                          ? "selected ring-4 ring-yellow-400 ring-opacity-75"
                          : ""
                      }
                      hover:shadow-xl transition-all duration-300
                    `}
                    >
                      <IconComponent className="text-lg" />

                      {/* Pulse Animation */}
                      <div className="absolute inset-0 rounded-full bg-white opacity-25 animate-ping"></div>

                      {/* Label */}
                      <div
                        className="map-tooltip absolute top-full mt-2 left-1/2 transform -translate-x-1/2
                                    text-white text-xs px-3 py-2 rounded-lg whitespace-nowrap
                                    opacity-0 hover:opacity-100 transition-opacity duration-300 z-20 shadow-lg"
                      >
                        {area.name}
                      </div>
                    </div>
                  </motion.div>
                );
              })}

              {/* Legend */}
              <div className="absolute bottom-4 left-4 bg-white bg-opacity-90 rounded-lg p-3 shadow-lg">
                <h4 className="font-semibold text-gray-800 mb-2 text-sm">
                  الدليل:
                </h4>
                <div className="space-y-1 text-xs">
                  <div className="flex items-center space-x-2">
                    <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                    <span>المباني الرئيسية</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-3 h-3 bg-gray-500 rounded-full"></div>
                    <span>مكاتب الدكاترة</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        ) : (
          // Google Maps Iframe
          <div>
            <div className="text-center mb-6">
              <h2 className="text-2xl font-bold text-gray-800 mb-2 arabic-text">
                موقع الكلية على خرائط Google
              </h2>
              <p className="text-gray-600 arabic-text">
                كلية الحاسبات والمعلومات - جامعة الزقازيق
              </p>
            </div>

            <div className="rounded-xl overflow-hidden shadow-lg">
              <iframe
                src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3434.671718444566!2d31.527055924749945!3d30.58681139267076!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x14f7f125ba86a5d3%3A0xb34e3748fa9ee184!2z2YPZhNmK2Kkg2KfZhNit2KfYs9io2KfYqiDZiNin2YTZhdi52YTZiNmF2KfYqiDYrNin2YXYudipINin2YTYstmC2KfYstmK2YI!5e0!3m2!1sar!2seg!4v1755863588087!5m2!1sar!2seg"
                width="100%"
                height="500"
                style={{ border: 0 }}
                allowFullScreen=""
                loading="lazy"
                referrerPolicy="no-referrer-when-downgrade"
                className="w-full"
              ></iframe>
            </div>
          </div>
        )}

        {/* Selected Area Details */}
        <AnimatePresence>
          {selectedArea && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: "auto" }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.3 }}
              className="area-details-modal mindmap-details bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-6 border border-blue-200"
            >
              <div className="flex items-start space-x-4">
                <div
                  className={`bg-gradient-to-br ${selectedArea.color} p-4 rounded-xl text-white shadow-lg`}
                >
                  <selectedArea.icon className="text-2xl" />
                </div>
                <div className="flex-1">
                  <h3 className="text-xl font-bold text-gray-800 mb-2">
                    {selectedArea.name}
                  </h3>
                  <p className="text-gray-600 mb-4">
                    {selectedArea.description}
                  </p>
                  <div className="space-y-2">
                    <h4 className="font-semibold text-gray-700">التفاصيل:</h4>
                    <ul className="grid grid-cols-1 md:grid-cols-2 gap-2">
                      {selectedArea.details.map((detail, index) => (
                        <li
                          key={index}
                          className="flex items-center space-x-2 text-gray-600"
                        >
                          <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                          <span className="text-sm">{detail}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
                <button
                  onClick={() => setSelectedArea(null)}
                  className="text-gray-400 hover:text-red-500 transition-colors p-2 hover:bg-gray-100 rounded-full"
                >
                  <FaTimes className="w-5 h-5" />
                </button>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Instructions */}
      <div className="student-card rounded-2xl p-6">
        <div className="text-center">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-green-500 to-blue-500 rounded-full mb-4">
            <FaInfoCircle className="text-2xl text-white" />
          </div>
          <h3 className="text-xl font-semibold text-gray-800 mb-3 arabic-text">
            كيفية الاستخدام
          </h3>
          <div className="space-y-2 text-gray-600">
            <p className="arabic-text">
              🎯 اضغط على النقاط الملونة في الخريطة التفاعلية لاستكشاف الأقسام
            </p>
            <p className="arabic-text">
              🗺️ استخدم خريطة Google للحصول على الاتجاهات الحقيقية
            </p>
            <p className="arabic-text">
              📍 كل نقطة تمثل مكان حقيقي داخل الكلية
            </p>
            <p className="arabic-text">
              💡 اضغط على زر الإغلاق (×) لإخفاء تفاصيل المكان
            </p>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export default Maps;
