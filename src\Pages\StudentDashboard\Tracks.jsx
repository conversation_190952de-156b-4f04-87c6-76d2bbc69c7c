import React, { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  FiCode, // Web Development
  FiSmartphone, // Mobile App Development
  FiDatabase, // Data Science
  FiShield, // Cybersecurity
  FiCloud, // Cloud Computing
  FiSettings, // DevOps
  FiCpu, // Embedded & IoT
  FiLayers, // Blockchain
  FiCheckSquare, // QA & Testing
  FiPenTool, // UI/UX Design
  FiArrowLeft, // Back button
  FiStar, // Star icon for milestones
} from "react-icons/fi";

import { GiBrain, GiGamepad } from "react-icons/gi";

const tracks = [
  {
    id: 1,
    title: "Web Development",
    icon: <FiCode />,
    roadmap: [
      "HTML Basics",
      "CSS Styling",
      "JavaScript Fundamentals",
      "Responsive Design",
      "Git & GitHub",
      "Frontend Frameworks (React/Vue/Angular)",
      "State Management",
      "APIs & REST",
      "GraphQL",
      "Backend Basics (Node.js/Express)",
      "Databases (SQL/NoSQL)",
      "Authentication & Security",
      "Testing (Unit/E2E)",
      "Deployment (CI/CD, Docker, Vercel, Netlify)",
      "Full Stack Projects & Portfolio",
    ],
  },
  {
    id: 2,
    title: "Mobile App Development",
    icon: <FiSmartphone />,
    roadmap: [
      "Java/Kotlin Basics (Android)",
      "Swift/Objective-C (iOS)",
      "Cross-platform (React Native, Flutter)",
      "UI Components & Styling",
      "State Management",
      "APIs Integration",
      "Authentication",
      "Local Storage & Databases",
      "Push Notifications",
      "App Security",
      "App Deployment (Google Play / App Store)",
      "CI/CD for Mobile",
      "Performance Optimization",
      "Testing (Unit/UI/Integration)",
      "Advanced Features (AR/VR, ML Integration)",
    ],
  },
  {
    id: 3,
    title: "Data Science",
    icon: <FiDatabase />,
    roadmap: [
      "Math & Statistics Fundamentals",
      "Python Basics",
      "Data Manipulation (Pandas, Numpy)",
      "Data Visualization (Matplotlib, Seaborn)",
      "Exploratory Data Analysis",
      "SQL & Databases",
      "Machine Learning Basics",
      "Supervised Learning",
      "Unsupervised Learning",
      "Deep Learning Basics",
      "NLP Basics",
      "Big Data Tools (Hadoop, Spark)",
      "Model Deployment",
      "MLOps Basics",
      "Capstone Projects",
    ],
  },
  {
    id: 4,
    title: "Cybersecurity",
    icon: <FiShield />,
    roadmap: [
      "Networking Fundamentals",
      "Operating Systems Security",
      "Linux Basics",
      "Cryptography",
      "Web Security",
      "Vulnerability Assessment",
      "Ethical Hacking",
      "Penetration Testing",
      "Security Tools (Wireshark, Metasploit)",
      "Malware Analysis",
      "Cloud Security",
      "Incident Response",
      "Digital Forensics",
      "Security Compliance & Laws",
      "Advanced Red/Blue Teaming",
    ],
  },
  {
    id: 5,
    title: "Cloud Computing",
    icon: <FiCloud />,
    roadmap: [
      "Cloud Fundamentals",
      "AWS Basics",
      "Azure Basics",
      "GCP Basics",
      "Virtualization",
      "Containers (Docker)",
      "Kubernetes",
      "Serverless Computing",
      "Networking in Cloud",
      "Cloud Databases",
      "Cloud Security",
      "CI/CD in Cloud",
      "Terraform & IaC",
      "Monitoring & Logging",
      "Advanced Cloud Architectures",
    ],
  },
  {
    id: 6,
    title: "DevOps & Automation",
    icon: <FiSettings />,
    roadmap: [
      "Linux Basics",
      "Shell Scripting",
      "Git & Version Control",
      "CI/CD Pipelines",
      "Docker",
      "Kubernetes",
      "Infrastructure as Code (Terraform)",
      "Monitoring Tools (Prometheus, Grafana)",
      "Logging & Tracing",
      "Cloud Providers",
      "Security in DevOps",
      "Automation Tools (Ansible, Puppet, Chef)",
      "Performance Testing",
      "DevSecOps",
      "Scaling & High Availability",
    ],
  },
  {
    id: 7,
    title: "Embedded Systems & IoT",
    icon: <FiCpu />,
    roadmap: [
      "C/C++ Basics",
      "Microcontrollers (Arduino, STM32)",
      "Embedded C Programming",
      "RTOS Basics",
      "IoT Fundamentals",
      "IoT Communication Protocols (MQTT, CoAP)",
      "Sensors & Actuators",
      "Wireless Communication (BLE, LoRa, Zigbee)",
      "Cloud IoT Platforms",
      "Edge Computing",
      "Security in IoT",
      "Firmware Development",
      "Testing Embedded Systems",
      "IoT Applications",
      "Smart Devices & Automation",
    ],
  },
  {
    id: 8,
    title: "Blockchain Development",
    icon: <FiLayers />,
    roadmap: [
      "Blockchain Fundamentals",
      "Cryptography Basics",
      "Ethereum Basics",
      "Smart Contracts",
      "Solidity Programming",
      "DApps Development",
      "Consensus Algorithms",
      "DeFi Basics",
      "NFTs",
      "Layer2 Solutions",
      "Blockchain Security",
      "Interoperability",
      "Scalability Solutions",
      "Testing Smart Contracts",
      "Blockchain Projects",
    ],
  },
  {
    id: 9,
    title: "Software Testing & QA",
    icon: <FiCheckSquare />,
    roadmap: [
      "Testing Fundamentals",
      "Manual Testing",
      "Agile & Scrum Basics",
      "Unit Testing",
      "Integration Testing",
      "E2E Testing",
      "Automation Testing (Selenium, Cypress)",
      "Performance Testing",
      "Security Testing",
      "Test Management Tools (Jira, TestRail)",
      "API Testing (Postman)",
      "Mobile App Testing",
      "CI/CD Testing",
      "Bug Reporting & Tracking",
      "QA Leadership & Strategy",
    ],
  },
  {
    id: 10,
    title: "UI/UX Design",
    icon: <FiPenTool />,
    roadmap: [
      "Design Fundamentals",
      "Color Theory & Typography",
      "Wireframing",
      "Prototyping (Figma, Adobe XD)",
      "User Research",
      "User Personas",
      "Accessibility Standards",
      "Design Systems",
      "Motion Design",
      "UI Patterns",
      "Responsive Design",
      "Usability Testing",
      "Collaboration with Developers",
      "DesignOps",
      "Portfolio & Case Studies",
    ],
  },
  {
    id: 11,
    title: "Machine Learning & AI",
    icon: <GiBrain />,
    roadmap: [
      "Math & Linear Algebra",
      "Python for ML",
      "Data Preprocessing",
      "Supervised ML",
      "Unsupervised ML",
      "Neural Networks",
      "Deep Learning",
      "CNNs",
      "RNNs",
      "Transformers",
      "Reinforcement Learning",
      "NLP Advanced",
      "MLOps & Deployment",
      "AI Ethics",
      "Research & Advanced Projects",
    ],
  },
  {
    id: 12,
    title: "Game Development",
    icon: <GiGamepad />,
    roadmap: [
      "Game Design Principles",
      "Programming Basics (C#/C++)",
      "Game Engines (Unity/Unreal)",
      "2D Game Development",
      "3D Game Development",
      "Physics in Games",
      "AI in Games",
      "Multiplayer Systems",
      "Animation & Rigging",
      "Audio in Games",
      "Shaders & Graphics",
      "Optimization Techniques",
      "Testing Games",
      "Publishing Games",
      "Monetization & Marketing",
    ],
  },
];

function Tracks() {
  const [selectedTrack, setSelectedTrack] = useState(null);
  const [selectedMilestone, setSelectedMilestone] = useState(null);

  const handleTrackSelect = (track) => {
    setSelectedTrack(track);
    setSelectedMilestone(null);
  };

  const handleBackToTracks = () => {
    setSelectedTrack(null);
    setSelectedMilestone(null);
  };

  const handleMilestoneClick = (index) => {
    setSelectedMilestone(index);
  };

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <AnimatePresence mode="wait">
        {!selectedTrack ? (
          <motion.div
            key="tracks-grid"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3 }}
            className="max-w-7xl mx-auto"
          >
            {/* Header */}
            <motion.div
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.4, delay: 0.1 }}
              className="text-center mb-12"
            >
              <h1 className="text-4xl font-bold mb-4 text-gray-800">
                Choose Your Career Track
              </h1>
              <p className="text-lg text-gray-600 mb-4">
                Explore your roadmap and start your journey 🚀
              </p>
              <div className="w-20 h-1 bg-blue-500 mx-auto rounded-full"></div>
            </motion.div>

            {/* Tracks Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {tracks.map((track, index) => (
                <motion.div
                  key={track.id}
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{
                    duration: 0.4,
                    delay: index * 0.05,
                  }}
                  whileHover={{
                    y: -5,
                    transition: { duration: 0.2 },
                  }}
                  whileTap={{ scale: 0.98 }}
                  onClick={() => handleTrackSelect(track)}
                  className="cursor-pointer"
                >
                  <div className="bg-white rounded-xl p-6 h-44 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-200 hover:border-blue-300">
                    {/* Content */}
                    <div className="h-full flex flex-col justify-between">
                      <div className="flex items-center justify-center mb-4">
                        <div className="text-4xl text-blue-600">
                          {track.icon}
                        </div>
                      </div>

                      <div className="text-center">
                        <h3 className="text-lg font-semibold text-gray-800 mb-2">
                          {track.title}
                        </h3>
                        <div className="text-sm text-gray-500">
                          {track.roadmap.length} milestones
                        </div>
                      </div>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        ) : (
          <RoadmapView
            track={selectedTrack}
            onBack={handleBackToTracks}
            selectedMilestone={selectedMilestone}
            onMilestoneClick={handleMilestoneClick}
          />
        )}
      </AnimatePresence>
    </div>
  );
}

// Roadmap View Component
const RoadmapView = ({
  track,
  onBack,
  selectedMilestone,
  onMilestoneClick,
}) => {
  return (
    <motion.div
      key="roadmap-view"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.3 }}
      className="max-w-4xl mx-auto"
    >
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.4, delay: 0.1 }}
        className="flex items-center justify-between mb-8"
      >
        <motion.button
          onClick={onBack}
          whileHover={{ x: -3 }}
          whileTap={{ scale: 0.98 }}
          className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-all duration-200"
        >
          <FiArrowLeft className="text-lg" />
          <span>Back to Tracks</span>
        </motion.button>
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-800 mb-1">
            {track.title} Roadmap
          </h1>
          <p className="text-gray-600">Your journey to mastery</p>
        </div>
        <div className="w-32"></div> {/* Spacer for centering */}
      </motion.div>

      {/* Roadmap Flowchart */}
      <div className="bg-white rounded-xl p-6 shadow-lg">
        {/* Milestones */}
        <div className="space-y-4">
          {track.roadmap.map((milestone, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{
                duration: 0.3,
                delay: index * 0.05,
              }}
              className="flex items-center"
            >
              <motion.div
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                onClick={() => onMilestoneClick(index)}
                className={`
                  relative cursor-pointer w-full
                  ${selectedMilestone === index ? "z-10" : "z-0"}
                `}
              >
                {/* Milestone Card */}
                <div
                  className={`
                  relative p-4 rounded-lg w-full flex items-center
                  ${
                    selectedMilestone === index
                      ? "bg-blue-50 border-2 border-blue-500"
                      : "bg-gray-50 border border-gray-200 hover:bg-gray-100"
                  }
                  transition-all duration-200
                `}
                >
                  {/* Milestone Number */}
                  <div
                    className={`
                    w-8 h-8 rounded-full flex items-center justify-center mr-4 flex-shrink-0
                    ${
                      selectedMilestone === index
                        ? "bg-blue-500 text-white"
                        : "bg-gray-300 text-gray-700"
                    }
                    text-sm font-bold
                  `}
                  >
                    {index + 1}
                  </div>

                  {/* Content */}
                  <div className="flex-1">
                    <h3
                      className={`
                      text-base font-medium
                      ${
                        selectedMilestone === index
                          ? "text-blue-700"
                          : "text-gray-800"
                      }
                    `}
                    >
                      {milestone}
                    </h3>

                    {selectedMilestone === index && (
                      <motion.div
                        initial={{ opacity: 0, height: 0 }}
                        animate={{ opacity: 1, height: "auto" }}
                        className="text-blue-600 text-sm mt-1"
                      >
                        <p>Click to explore this milestone in detail</p>
                      </motion.div>
                    )}
                  </div>

                  {/* Star Icon */}
                  <div
                    className={`
                      text-lg ml-2
                      ${
                        selectedMilestone === index
                          ? "text-blue-500"
                          : "text-gray-400"
                      }
                    `}
                  >
                    <FiStar />
                  </div>
                </div>
              </motion.div>
            </motion.div>
          ))}
        </div>
      </div>
    </motion.div>
  );
};

export default Tracks;
