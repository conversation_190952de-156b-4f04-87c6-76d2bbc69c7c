import React, { useState } from "react";
import { motion } from "framer-motion";
import {
  FaUniversity,
  FaBook,
  FaChalkboardTeacher,
  FaFlask,
  FaUserTie,
  FaUserGraduate,
  FaCoffee,
  FaBuilding,
  FaUsers,
} from "react-icons/fa";

// مكون تجريبي مبسط للخريطة الذهنية
const MindMapDemo = () => {
  const [selectedArea, setSelectedArea] = useState(null);

  const demoAreas = [
    {
      id: 1,
      name: "مدخل الكلية",
      icon: FaUniversity,
      color: "from-blue-500 to-blue-600",
      description: "البوابة الرئيسية للكلية"
    },
    {
      id: 2,
      name: "المكتبة",
      icon: FaBook,
      color: "from-green-500 to-green-600",
      description: "مكتبة الكلية ومصادر التعلم"
    },
    {
      id: 3,
      name: "المحاضرات",
      icon: FaChalkboardTeacher,
      color: "from-purple-500 to-purple-600",
      description: "قاعات المحاضرات الرئيسية"
    },
    {
      id: 4,
      name: "المعامل",
      icon: FaFlask,
      color: "from-red-500 to-red-600",
      description: "المعامل والمختبرات التقنية"
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 p-8">
      <div className="max-w-6xl mx-auto">
        {/* العنوان */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-800 mb-4">
            الخريطة الذهنية - نسخة تجريبية
          </h1>
          <p className="text-gray-600">
            كلية الحاسبات والمعلومات - جامعة الزقازيق
          </p>
        </div>

        {/* الأيقونة المركزية */}
        <div className="text-center mb-12">
          <div className="inline-flex items-center justify-center w-24 h-24 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full mb-4 shadow-xl">
            <FaUniversity className="text-4xl text-white" />
          </div>
          <h2 className="text-2xl font-bold text-gray-800">
            كلية الحاسبات والمعلومات
          </h2>
        </div>

        {/* شبكة الأقسام */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {demoAreas.map((area, index) => {
            const IconComponent = area.icon;
            return (
              <motion.div
                key={area.id}
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5, delay: index * 0.2 }}
                whileHover={{ scale: 1.05, y: -5 }}
                whileTap={{ scale: 0.95 }}
                className={`
                  cursor-pointer rounded-xl p-6 text-center
                  bg-gradient-to-br ${area.color} text-white
                  shadow-lg hover:shadow-xl transition-all duration-300
                  ${selectedArea?.id === area.id ? 'ring-4 ring-blue-300' : ''}
                `}
                onClick={() => setSelectedArea(selectedArea?.id === area.id ? null : area)}
              >
                <div className="flex flex-col items-center space-y-3">
                  <div className="bg-white bg-opacity-20 p-4 rounded-full">
                    <IconComponent className="text-3xl" />
                  </div>
                  <h3 className="font-bold text-lg">{area.name}</h3>
                  <p className="text-sm opacity-90">{area.description}</p>
                </div>
              </motion.div>
            );
          })}
        </div>

        {/* تفاصيل القسم المحدد */}
        {selectedArea && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white rounded-xl p-8 shadow-xl border border-gray-200"
          >
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-2xl font-bold text-gray-800">
                {selectedArea.name}
              </h3>
              <button
                onClick={() => setSelectedArea(null)}
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <p className="text-gray-600 text-lg">
              {selectedArea.description}
            </p>
            <div className="mt-6 p-4 bg-blue-50 rounded-lg">
              <p className="text-blue-800 font-medium">
                💡 اضغط على البطاقات الأخرى لاستكشاف المزيد من الأقسام
              </p>
            </div>
          </motion.div>
        )}

        {/* تعليمات الاستخدام */}
        <div className="mt-12 text-center">
          <div className="bg-white rounded-xl p-6 shadow-lg">
            <h3 className="text-lg font-semibold text-gray-800 mb-2">
              كيفية الاستخدام
            </h3>
            <p className="text-gray-600">
              اضغط على أي بطاقة لعرض تفاصيل أكثر عن القسم. 
              يمكنك النقر مرة أخرى لإخفاء التفاصيل.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MindMapDemo;
